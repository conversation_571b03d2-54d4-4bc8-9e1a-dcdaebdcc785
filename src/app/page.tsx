"use client";
import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/context/auth";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { MessageCircle, User, UserCheck, Shield } from "lucide-react";
import Link from "next/link";

export default function Home() {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      router.push("/chat");
    }
  }, [isAuthenticated, isLoading, router]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (isAuthenticated) {
    return null; // Will redirect to /chat
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="max-w-4xl w-full">
        <div className="text-center mb-12">
          <div className="mx-auto h-16 w-16 text-blue-600 mb-6">
            <MessageCircle className="h-16 w-16" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Healthcare Chat Portal
          </h1>
          <p className="text-xl text-gray-600 mb-8">
            Secure communication platform for patients, providers, and
            administrators
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-6 mb-8">
          {/* Patient Login */}
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="text-center">
              <div className="mx-auto h-12 w-12 text-blue-600 mb-4">
                <User className="h-12 w-12" />
              </div>
              <CardTitle>Patient Portal</CardTitle>
              <CardDescription>
                Access your consultation chat and communicate with your
                healthcare provider
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Link href="/login/patient">
                <Button className="w-full">Patient Login</Button>
              </Link>
            </CardContent>
          </Card>

          {/* Provider Login */}
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="text-center">
              <div className="mx-auto h-12 w-12 text-green-600 mb-4">
                <UserCheck className="h-12 w-12" />
              </div>
              <CardTitle>Provider Portal</CardTitle>
              <CardDescription>
                Manage patient consultations and provide healthcare services
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Link href="/login/provider">
                <Button className="w-full bg-green-600 hover:bg-green-700">
                  Provider Login
                </Button>
              </Link>
            </CardContent>
          </Card>

          {/* Admin Login */}
          <Card className="hover:shadow-lg transition-shadow">
            <CardHeader className="text-center">
              <div className="mx-auto h-12 w-12 text-purple-600 mb-4">
                <Shield className="h-12 w-12" />
              </div>
              <CardTitle>Admin Portal</CardTitle>
              <CardDescription>
                System administration and management access
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Link href="/login/admin">
                <Button className="w-full bg-purple-600 hover:bg-purple-700">
                  Admin Login
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>

        <div className="text-center">
          <Link href="/login">
            <Button variant="outline" size="lg">
              General Login
            </Button>
          </Link>
        </div>

        <div className="mt-12 text-center text-sm text-gray-500">
          <p>Secure • HIPAA Compliant • 24/7 Support</p>
        </div>
      </div>
    </div>
  );
}
