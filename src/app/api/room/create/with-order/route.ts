import { NextRequest, NextResponse } from 'next/server';
import { apiServer } from '@/lib/apiServer';
import { 
  authenticateRequest,
  createApiResponse, 
  createErrorResponse,
  isValidUUID 
} from '@/lib/auth-middleware';
import { 
  CreateRoomWithOrderRequest, 
  CreateRoomWithOrderResponse,
  EnhancedUserRole 
} from '@/types/chat';

/**
 * Advanced Room Creation with Order Validation
 * POST /api/room/create/with-order
 * 
 * Creates room with orderGuid and role validation. Validates provider/user order relationships.
 */
export async function POST(request: NextRequest) {
  try {
    const body: CreateRoomWithOrderRequest = await request.json();
    const { order_guid, role } = body;

    // Validate required fields
    if (!order_guid || !role) {
      return NextResponse.json(
        createErrorResponse(400, 'Order GUID and role required'),
        { status: 400 }
      );
    }

    // Validate UUID format
    if (!isValidUUID(order_guid)) {
      return NextResponse.json(
        createErrorResponse(400, 'Invalid order GUID format'),
        { status: 400 }
      );
    }

    // Validate role
    const validRoles: EnhancedUserRole[] = [
      'USER', 'BUSER', 'SUPPORT_ADMIN', 'GROUP_ADMIN', 'medical_assistant', 'pharmacist'
    ];
    if (!validRoles.includes(role)) {
      return NextResponse.json(
        createErrorResponse(400, 'Invalid role provided'),
        { status: 400 }
      );
    }

    // Authenticate request
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        createErrorResponse(401, 'Unauthorized - Invalid token'),
        { status: 401 }
      );
    }

    const user = authResult.user;
    const token = request.headers.get('authorization')?.replace('Bearer ', '');

    // Step 1: Validate order exists
    const orderResponse = await apiServer({
      method: 'GET',
      endpoint: `order/${order_guid}`,
      token,
    });

    if (orderResponse.statusCode === 404) {
      return NextResponse.json(
        createErrorResponse(404, 'Order not found'),
        { status: 404 }
      );
    }

    if (orderResponse.statusCode !== 200) {
      return NextResponse.json(
        createErrorResponse(500, 'Failed to validate order'),
        { status: 500 }
      );
    }

    const order = orderResponse.data;

    // Step 2: Validate user authorization for the order
    const isAuthorized = await validateOrderAccess(user, order, role);
    if (!isAuthorized) {
      return NextResponse.json(
        createErrorResponse(403, 'Not authorized to create room for this order'),
        { status: 403 }
      );
    }

    // Step 3: Check if room already exists for this order
    const existingRoomResponse = await apiServer({
      method: 'GET',
      endpoint: `room/by-order/${order_guid}`,
      token,
    });

    let isNewRoom = true;
    let roomData;

    if (existingRoomResponse.statusCode === 200) {
      // Room exists, add user if not already a member
      isNewRoom = false;
      roomData = existingRoomResponse.data;

      const addMemberResponse = await apiServer({
        method: 'POST',
        endpoint: `room/${roomData.room_identifier}/add-member`,
        data: {
          user_id: user.user_id,
          role: role,
        },
        token,
      });

      if (addMemberResponse.statusCode !== 200 && addMemberResponse.statusCode !== 409) {
        return NextResponse.json(
          createErrorResponse(500, 'Failed to add user to existing room'),
          { status: 500 }
        );
      }
    } else {
      // Step 4: Create new room
      const createRoomResponse = await apiServer({
        method: 'POST',
        endpoint: 'room/create',
        data: {
          order_guid,
          service_key: order.service_key,
          room_name: `Order ${order_guid.substring(0, 8)}`,
          creator_id: user.user_id,
          creator_role: role,
        },
        token,
      });

      if (createRoomResponse.statusCode !== 200) {
        return NextResponse.json(
          createErrorResponse(500, 'Failed to create room'),
          { status: 500 }
        );
      }

      roomData = createRoomResponse.data;
    }

    // Format response according to API specification
    const response: CreateRoomWithOrderResponse = {
      statusCode: 200,
      data: {
        room: {
          id: roomData.id,
          room_identifier: roomData.room_identifier,
          room_name: roomData.room_name,
          service_key: roomData.service_key || order.service_key,
        },
        room_url: `/chat/${roomData.room_identifier}`,
        is_new_room: isNewRoom,
        order_guid,
      },
      message: isNewRoom ? 'Room created successfully' : 'Existing room found and user added',
      success: true,
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Create room with order error:', error);
    return NextResponse.json(
      createErrorResponse(500, 'Internal server error'),
      { status: 500 }
    );
  }
}

/**
 * Validates if user has access to create/join room for the given order
 */
async function validateOrderAccess(user: any, order: any, role: EnhancedUserRole): Promise<boolean> {
  try {
    // Admin roles can access any order
    if (role === 'SUPPORT_ADMIN' || role === 'GROUP_ADMIN') {
      return true;
    }

    // Provider (BUSER) must be the assigned provider for the order
    if (role === 'BUSER') {
      return order.provider_id === user.user_id;
    }

    // User must be the patient who answered the order
    if (role === 'USER') {
      return order.answer_given_by === user.user_id;
    }

    // Medical assistant and pharmacist roles need specific validation
    if (role === 'medical_assistant' || role === 'pharmacist') {
      // These roles can access orders from their organization/group
      return order.organization_id === user.organization_id;
    }

    return false;
  } catch (error) {
    console.error('Order access validation error:', error);
    return false;
  }
}
