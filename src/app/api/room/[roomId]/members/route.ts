import { NextRequest, NextResponse } from 'next/server';
import { apiServer } from '@/lib/apiServer';
import { 
  authenticateRequest,
  createApiResponse, 
  createErrorResponse 
} from '@/lib/auth-middleware';
import { RoomMembersResponse } from '@/types/chat';

/**
 * Get Room Members (Token Protected)
 * GET /api/room/[roomId]/members
 * 
 * Retrieves room member names and roles. User must be part of the room.
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { roomId: string } }
) {
  try {
    const { roomId } = params;

    // Validate room ID
    if (!roomId) {
      return NextResponse.json(
        createErrorResponse(400, 'Room ID required'),
        { status: 400 }
      );
    }

    // Authenticate request
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        createErrorResponse(401, 'Unauthorized - Invalid token'),
        { status: 401 }
      );
    }

    const user = authResult.user;

    // Step 1: Validate user is a member of the room
    const membershipResponse = await apiServer({
      method: 'GET',
      endpoint: `room/${roomId}/validate-membership`,
      token: request.headers.get('authorization')?.replace('Bearer ', ''),
    });

    if (membershipResponse.statusCode === 403) {
      return NextResponse.json(
        createErrorResponse(403, 'Not authorized to view room members'),
        { status: 403 }
      );
    }

    if (membershipResponse.statusCode === 404) {
      return NextResponse.json(
        createErrorResponse(404, 'Room not found'),
        { status: 404 }
      );
    }

    if (membershipResponse.statusCode !== 200) {
      return NextResponse.json(
        createErrorResponse(500, 'Failed to validate room membership'),
        { status: 500 }
      );
    }

    // Step 2: Get room members
    const membersResponse = await apiServer({
      method: 'GET',
      endpoint: `room/${roomId}/members`,
      token: request.headers.get('authorization')?.replace('Bearer ', ''),
    });

    if (membersResponse.statusCode !== 200) {
      return NextResponse.json(
        createErrorResponse(500, 'Failed to retrieve room members'),
        { status: 500 }
      );
    }

    // Step 3: Get room information
    const roomResponse = await apiServer({
      method: 'GET',
      endpoint: `room/${roomId}/info`,
      token: request.headers.get('authorization')?.replace('Bearer ', ''),
    });

    // Format response according to API specification
    const response: RoomMembersResponse = {
      statusCode: 200,
      data: {
        room: {
          id: roomResponse.data?.room?.id || parseInt(roomId),
          room_identifier: roomResponse.data?.room?.room_identifier || roomId,
          room_name: roomResponse.data?.room?.room_name || `Room ${roomId}`,
        },
        members: membersResponse.data.members.map((member: any) => ({
          user_id: member.user_id,
          name: `${member.first_name} ${member.last_name}`.trim(),
          first_name: member.first_name,
          last_name: member.last_name,
          email: member.email,
          role: member.role,
          status: member.status || 'active',
        })),
        total_members: membersResponse.data.members.length,
      },
      message: 'Room members retrieved successfully',
      success: true,
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Get room members error:', error);
    return NextResponse.json(
      createErrorResponse(500, 'Internal server error'),
      { status: 500 }
    );
  }
}
