import { NextRequest, NextResponse } from 'next/server';
import { apiServer } from '@/lib/apiServer';
import { 
  createApiResponse, 
  createErrorResponse, 
  isValidEmail 
} from '@/lib/auth-middleware';
import { 
  EnhancedOtpRequest, 
  EnhancedOtpResponse, 
  EnhancedUserRole 
} from '@/types/chat';

/**
 * Enhanced OTP Sending with Room Validation
 * POST /api/auth/send-otp-with-validation
 * 
 * Sends OTP to user with room validation ensuring the user is part of the specified room.
 */
export async function POST(request: NextRequest) {
  try {
    const body: EnhancedOtpRequest = await request.json();
    const { room_identifier, email, role } = body;

    // Validate required fields
    if (!room_identifier || !email || !role) {
      return NextResponse.json(
        createErrorResponse(400, 'Missing required fields: room_identifier, email, and role are required'),
        { status: 400 }
      );
    }

    // Validate email format
    if (!isValidEmail(email)) {
      return NextResponse.json(
        createErrorResponse(400, 'Invalid email format'),
        { status: 400 }
      );
    }

    // Validate role
    const validRoles: EnhancedUserRole[] = [
      'USER', 'BUSER', 'SUPPORT_ADMIN', 'GROUP_ADMIN', 'medical_assistant', 'pharmacist'
    ];
    if (!validRoles.includes(role)) {
      return NextResponse.json(
        createErrorResponse(400, 'Invalid role provided'),
        { status: 400 }
      );
    }

    // Step 1: Validate room exists and is active
    const roomValidationResponse = await apiServer({
      method: 'GET',
      endpoint: `room/${room_identifier}/validate`,
    });

    if (roomValidationResponse.statusCode !== 200) {
      return NextResponse.json(
        createErrorResponse(404, 'Room not found or inactive'),
        { status: 404 }
      );
    }

    // Step 2: Validate user exists and has access to the room
    const userValidationResponse = await apiServer({
      method: 'POST',
      endpoint: 'auth/validate-user-room-access',
      data: {
        room_identifier,
        email,
        role,
      },
    });

    if (userValidationResponse.statusCode === 403) {
      return NextResponse.json(
        createErrorResponse(403, 'User not authorized for this room'),
        { status: 403 }
      );
    }

    if (userValidationResponse.statusCode !== 200) {
      return NextResponse.json(
        createErrorResponse(404, 'User not found or not a member of this room'),
        { status: 404 }
      );
    }

    // Step 3: Send OTP
    const otpResponse = await apiServer({
      method: 'POST',
      endpoint: 'auth/send-otp',
      data: {
        email,
        role,
        room_identifier,
      },
    });

    if (otpResponse.statusCode !== 200) {
      return NextResponse.json(
        createErrorResponse(500, 'Failed to send OTP'),
        { status: 500 }
      );
    }

    // Format response according to API specification
    const response: EnhancedOtpResponse = {
      statusCode: 200,
      data: {
        user: {
          user_guid: userValidationResponse.data.user.user_guid,
          first_name: userValidationResponse.data.user.first_name,
          last_name: userValidationResponse.data.user.last_name,
          email: userValidationResponse.data.user.email,
          role: userValidationResponse.data.user.role,
        },
        room: {
          id: roomValidationResponse.data.room.id,
          room_identifier: roomValidationResponse.data.room.room_identifier,
          room_name: roomValidationResponse.data.room.room_name,
        },
      },
      message: 'OTP has been sent to your email and phone',
      success: true,
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Send OTP with validation error:', error);
    return NextResponse.json(
      createErrorResponse(500, 'Internal server error'),
      { status: 500 }
    );
  }
}
