import { NextRequest, NextResponse } from 'next/server';
import { apiServer } from '@/lib/apiServer';
import { 
  createApiResponse, 
  createErrorResponse, 
  isValidEmail,
  isValidOTP 
} from '@/lib/auth-middleware';
import { 
  EnhancedOtpVerificationRequest, 
  EnhancedOtpVerificationResponse 
} from '@/types/chat';

/**
 * Enhanced OTP Verification
 * POST /api/auth/verify-otp
 * 
 * Verifies OTP using email and returns enhanced user token with improved validation.
 */
export async function POST(request: NextRequest) {
  try {
    const body: EnhancedOtpVerificationRequest = await request.json();
    const { email, otp } = body;

    // Validate required fields
    if (!email || !otp) {
      return NextResponse.json(
        createErrorResponse(400, 'Missing required fields: email and otp are required'),
        { status: 400 }
      );
    }

    // Validate email format
    if (!isValidEmail(email)) {
      return NextResponse.json(
        createErrorResponse(400, 'Invalid email format'),
        { status: 400 }
      );
    }

    // Validate OTP format (4-6 characters)
    if (!isValidOTP(otp)) {
      return NextResponse.json(
        createErrorResponse(400, 'Invalid OTP format. OTP must be 4-6 digits'),
        { status: 400 }
      );
    }

    // Step 1: Verify OTP with backend
    const verificationResponse = await apiServer({
      method: 'POST',
      endpoint: 'auth/verify-otp',
      data: {
        email,
        otp,
      },
    });

    if (verificationResponse.statusCode === 400) {
      return NextResponse.json(
        createErrorResponse(400, 'Invalid OTP or OTP expired'),
        { status: 400 }
      );
    }

    if (verificationResponse.statusCode === 404) {
      return NextResponse.json(
        createErrorResponse(404, 'User not found'),
        { status: 404 }
      );
    }

    if (verificationResponse.statusCode !== 200) {
      return NextResponse.json(
        createErrorResponse(500, 'Failed to verify OTP'),
        { status: 500 }
      );
    }

    // Step 2: Get enhanced user information
    const userResponse = await apiServer({
      method: 'GET',
      endpoint: `user/profile/${verificationResponse.data.user.user_id}`,
      token: verificationResponse.data.token,
    });

    if (userResponse.statusCode !== 200) {
      // Fallback to basic user info from verification response
      console.warn('Could not fetch enhanced user profile, using basic info');
    }

    // Format response according to API specification
    const userData = userResponse.statusCode === 200 ? userResponse.data : verificationResponse.data.user;
    
    const response: EnhancedOtpVerificationResponse = {
      statusCode: 200,
      data: {
        user: {
          user_id: userData.user_id || userData.id,
          user_guid: userData.user_guid,
          first_name: userData.first_name,
          last_name: userData.last_name,
          email: userData.email,
          role: userData.role,
          status: userData.status || 'active',
        },
        token: verificationResponse.data.token,
        expires_in: '2h', // Standard token expiration
      },
      message: 'OTP verified successfully',
      success: true,
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Enhanced OTP verification error:', error);
    return NextResponse.json(
      createErrorResponse(500, 'Internal server error'),
      { status: 500 }
    );
  }
}
