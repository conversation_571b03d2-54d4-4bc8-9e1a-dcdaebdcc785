"use client";
import React, { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth, withAuth } from '@/context/auth';
import { apiClient } from '@/lib/api';
import { ChatRoom, ApiResponse, ChatSettings } from '@/types/chat';
import UnifiedChatComponent from '@/components/chat/unified-chat';
import { Loader } from '@/components/shared/loader';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { MessageCircle, Settings, Users } from 'lucide-react';
import { toast } from 'sonner';

/**
 * Unified Chat Page
 * Handles all roles (USER, PROVIDER, ADMIN) with role-based features
 */
const ChatPage: React.FC = () => {
  const { user, token, logout } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const roomId = searchParams.get('room');
  
  const [chatData, setChatData] = useState<any>(null);
  const [chatSettings, setChatSettings] = useState<ChatSettings>({ isEnabled: true, canToggle: false });
  const [isLoading, setIsLoading] = useState(true);
  const [roomIdInput, setRoomIdInput] = useState(roomId || '');

  // Determine if user can toggle chat (PROVIDER and ADMIN only)
  const canToggleChat = user?.role === 'PROVIDER' || user?.role === 'ADMIN';
  
  // Determine if chat is disabled for USER
  const isChatDisabledForUser = user?.role === 'USER' && !chatSettings.isEnabled;

  useEffect(() => {
    if (roomId) {
      loadChatData(roomId);
    } else {
      setIsLoading(false);
    }
  }, [roomId]);

  const loadChatData = async (roomIdentifier: string) => {
    if (!token) return;
    
    setIsLoading(true);
    try {
      // Load chat metadata
      const response: ApiResponse = await apiClient({
        method: "GET",
        endpoint: `chat/${roomIdentifier}/metadata`,
        token,
      });

      if (response.statusCode === 200) {
        setChatData(response.data);
        
        // Load chat settings for PROVIDER/ADMIN
        if (canToggleChat) {
          await loadChatSettings(roomIdentifier);
        }
      } else {
        toast.error(response.message || 'Failed to load chat data');
      }
    } catch (error) {
      console.error('Failed to load chat data:', error);
      toast.error('Failed to load chat data');
    } finally {
      setIsLoading(false);
    }
  };

  const loadChatSettings = async (roomIdentifier: string) => {
    if (!token) return;
    
    try {
      const response: ApiResponse = await apiClient({
        method: "GET",
        endpoint: `room/${roomIdentifier}/settings`,
        token,
      });

      if (response.statusCode === 200) {
        setChatSettings({
          isEnabled: response.data.isEnabled,
          canToggle: canToggleChat,
        });
      }
    } catch (error) {
      console.error('Failed to load chat settings:', error);
      // Default to enabled if we can't load settings
      setChatSettings({ isEnabled: true, canToggle: canToggleChat });
    }
  };

  const toggleChatStatus = async () => {
    if (!roomId || !token || !canToggleChat) return;
    
    try {
      const newStatus = !chatSettings.isEnabled;
      const response: ApiResponse = await apiClient({
        method: "POST",
        endpoint: `room/${roomId}/${newStatus ? 'enable' : 'disable'}`,
        data: {},
        token,
      });

      if (response.statusCode === 200) {
        setChatSettings(prev => ({ ...prev, isEnabled: newStatus }));
        toast.success(`Chat ${newStatus ? 'enabled' : 'disabled'} successfully`);
      } else {
        toast.error(response.message || 'Failed to toggle chat status');
      }
    } catch (error) {
      console.error('Failed to toggle chat status:', error);
      toast.error('Failed to toggle chat status');
    }
  };

  const handleRoomSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (roomIdInput.trim()) {
      router.push(`/chat?room=${roomIdInput.trim()}`);
    }
  };

  const getRoleDisplayName = () => {
    switch (user?.role) {
      case 'USER': return 'Patient';
      case 'PROVIDER': return 'Healthcare Provider';
      case 'ADMIN': return 'Administrator';
      default: return 'User';
    }
  };

  if (isLoading) {
    return <Loader show={true} />;
  }

  // Show room selection if no room is specified
  if (!roomId) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto h-12 w-12 text-blue-600 mb-4">
              <MessageCircle className="h-12 w-12" />
            </div>
            <CardTitle>Welcome, {user?.first_name}!</CardTitle>
            <CardDescription>
              You are logged in as: {getRoleDisplayName()}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleRoomSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="roomId">Enter Room ID</Label>
                <Input
                  id="roomId"
                  type="text"
                  value={roomIdInput}
                  onChange={(e) => setRoomIdInput(e.target.value)}
                  placeholder="Enter room ID to join chat"
                  required
                />
              </div>
              <Button type="submit" className="w-full">
                Join Chat Room
              </Button>
            </form>
            
            <div className="mt-6 pt-6 border-t">
              <Button 
                variant="outline" 
                onClick={logout}
                className="w-full"
              >
                Logout
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show disabled message for USER when chat is disabled
  if (isChatDisabledForUser) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md text-center">
          <CardHeader>
            <div className="mx-auto h-12 w-12 text-gray-400 mb-4">
              <MessageCircle className="h-12 w-12" />
            </div>
            <CardTitle>Chat Unavailable</CardTitle>
            <CardDescription>
              The chat is currently disabled. Please wait for your healthcare provider to enable it.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              variant="outline" 
              onClick={() => router.push('/chat')}
              className="w-full"
            >
              Back to Room Selection
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show chat interface
  if (!chatData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md text-center">
          <CardHeader>
            <CardTitle>Room Not Found</CardTitle>
            <CardDescription>
              The specified chat room could not be found.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={() => router.push('/chat')}
              className="w-full"
            >
              Back to Room Selection
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col">
      {/* Chat Controls for PROVIDER/ADMIN */}
      {canToggleChat && (
        <div className="bg-white border-b px-4 py-2 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Settings className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium">Chat Controls</span>
          </div>
          <div className="flex items-center space-x-2">
            <Label htmlFor="chat-toggle" className="text-sm">
              {chatSettings.isEnabled ? 'Enabled' : 'Disabled'}
            </Label>
            <Switch
              id="chat-toggle"
              checked={chatSettings.isEnabled}
              onCheckedChange={toggleChatStatus}
            />
          </div>
        </div>
      )}
      
      {/* Chat Interface */}
      <div className="flex-1">
        <UnifiedChatComponent
          patient={chatData.patient}
          provider={chatData.provider}
          room={chatData.room}
          role={user?.role || 'USER'}
        />
      </div>
    </div>
  );
};

export default withAuth(ChatPage);
