import { getCookie } from "@/actions/cookies";
import UserChatUICompoent from "@/components/chat-ui/user-chat";
import ChatViewPage from "@/components/chat/chat-view";
import ErrorRendered from "@/components/shared/error/error-renderer";
import { apiServer } from "@/lib/apiServer";
import CONSTANT from "@/lib/constant";
import React from "react";
export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

interface Props {
  params: {
    roomId: string;
  };
}

const UserChatPage = async ({ params }: Props) => {
  const roomId = params.roomId;
  // const token = await getCookie(CONSTANT.ACCESS_TOKEN);
  // const res = await apiServer('GET', `chat/${roomId}/metadata`, {}, token);
  // const patient = res?.data?.patient;
  // const provider = res?.data?.provider;
  // const messages = res?.data?.messages;
  // const room = res?.data?.room;
  const token = await getCookie(CONSTANT.ACCESS_TOKEN);
  const res = await apiServer("GET", `room/${roomId}/exits-consult`, {}, token);
  if (res?.statusCode >= 400) {
    return <ErrorRendered statusCode={res.statusCode} />;
  }
  return (
    <>
      {/* <ChatViewPage patient={patient} provider={provider} messages={messages} room={room} role='USER' />
       */}

      <UserChatUICompoent
        roomIdentifier={roomId}
        role={"USER"}
        isConsult={res?.data?.consultExists?.length > 0}
        token={token}
      />
    </>
  );
};

export default UserChatPage;
