"use client";
import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { apiClient } from '@/lib/api';
import { tokenManager } from '@/lib/tokenManager';
import { 
  User, 
  UserRole, 
  AuthContextType, 
  LoginRequest, 
  OtpRequest, 
  ApiResponse,
  API_ENDPOINTS 
} from '@/types/chat';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: React.ReactNode;
}

/**
 * Unified Authentication Provider
 * Handles authentication for USER, PROVIDER, and ADMIN roles
 * Supports email + OTP login flow with proper token management
 */
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pendingLogin, setPendingLogin] = useState<LoginRequest | null>(null);
  const router = useRouter();

  // Initialize authentication state
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // Migrate legacy tokens if needed
        await tokenManager.migrateLegacyTokens();
        
        const authToken = await tokenManager.getToken();
        const authUser = await tokenManager.getUser();
        
        if (authToken && authUser) {
          setToken(authToken);
          setUser(authUser);
        }
      } catch (err) {
        console.error('Failed to initialize auth:', err);
        setError('Failed to initialize authentication');
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  /**
   * Step 1: Send OTP to email for specific role
   */
  const login = useCallback(async (email: string, role: UserRole): Promise<boolean> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response: ApiResponse = await apiClient({
        method: "POST",
        endpoint: API_ENDPOINTS.AUTH.SEND_OTP,
        data: { email, role },
      });

      if (response.statusCode === 200) {
        setPendingLogin({ email, role });
        toast.success('OTP sent to your email');
        return true;
      } else {
        setError(response.message || 'Failed to send OTP');
        toast.error(response.message || 'Failed to send OTP');
        return false;
      }
    } catch (err: any) {
      const errorMessage = err?.message || 'Network error occurred';
      setError(errorMessage);
      toast.error(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Step 2: Verify OTP and complete authentication
   */
  const verifyOtp = useCallback(async (otp: string): Promise<boolean> => {
    if (!pendingLogin) {
      setError('No pending login found');
      return false;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response: ApiResponse = await apiClient({
        method: "POST",
        endpoint: API_ENDPOINTS.AUTH.VERIFY_OTP,
        data: {
          email: pendingLogin.email,
          otp,
          role: pendingLogin.role,
        } as OtpRequest,
      });

      if (response.statusCode === 200) {
        const { token: authToken, user: authUser } = response.data;
        
        // Ensure user has the correct role
        const userWithRole = { ...authUser, role: pendingLogin.role };
        
        // Store authentication data
        await tokenManager.setToken(authToken, userWithRole);
        setToken(authToken);
        setUser(userWithRole);
        setPendingLogin(null);
        
        toast.success('Login successful!');
        
        // Redirect to chat
        router.push('/chat');
        return true;
      } else {
        setError(response.message || 'Invalid OTP');
        toast.error(response.message || 'Invalid OTP');
        return false;
      }
    } catch (err: any) {
      const errorMessage = err?.message || 'OTP verification failed';
      setError(errorMessage);
      toast.error(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [pendingLogin, router]);

  /**
   * Logout and clear authentication data
   */
  const logout = useCallback(async (): Promise<void> => {
    try {
      await tokenManager.removeToken();
      setToken(null);
      setUser(null);
      setPendingLogin(null);
      setError(null);
      
      toast.success('Logged out successfully');
      router.push('/');
    } catch (err) {
      console.error('Logout failed:', err);
      toast.error('Logout failed');
    }
  }, [router]);

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const value: AuthContextType = {
    user,
    token,
    isAuthenticated: !!token && !!user,
    isLoading,
    error,
    login,
    verifyOtp,
    logout,
    clearError,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

/**
 * Hook to use authentication context
 */
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

/**
 * Higher-order component for route protection
 */
export const withAuth = <P extends object>(
  Component: React.ComponentType<P>,
  allowedRoles?: UserRole[]
) => {
  return function AuthenticatedComponent(props: P) {
    const { isAuthenticated, user, isLoading } = useAuth();
    const router = useRouter();

    useEffect(() => {
      if (!isLoading) {
        if (!isAuthenticated) {
          router.push('/');
          return;
        }

        if (allowedRoles && user && !allowedRoles.includes(user.role)) {
          toast.error('Access denied for your role');
          router.push('/');
          return;
        }
      }
    }, [isAuthenticated, user, isLoading, router]);

    if (isLoading) {
      return (
        <div className="flex items-center justify-center h-screen">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
        </div>
      );
    }

    if (!isAuthenticated) {
      return null;
    }

    if (allowedRoles && user && !allowedRoles.includes(user.role)) {
      return null;
    }

    return <Component {...props} />;
  };
};

export default AuthProvider;
