import { getCookie } from '@/actions/cookies';
import axios, { AxiosRequestConfig } from "axios";
import { cookies } from "next/headers";
import CONSTANT from './constant';
const apilink = process.env.NEXT_PUBLIC_BASE_URL;

export const apiServer = async (method: string, endpoint: string, data: any, token?: string) => {
  const options = {
    baseURL: `${apilink}/api/v1/`,
    method: method,
    url: endpoint,
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
  } as AxiosRequestConfig;

  if (method === "POST" || method === "PUT") {
    options.data = data;
  }

  try {
    const { data } = await axios.request(options);
    return data;
  } catch (error: any) {
    return error?.response?.data;
  }
};