import { NextRequest } from 'next/server';
import jwt from 'jsonwebtoken';
import { User } from '@/types/chat';

export interface AuthenticatedRequest extends NextRequest {
  user?: User;
}

export interface AuthValidationResult {
  success: boolean;
  user?: User;
  error?: string;
}

/**
 * Validates JWT token and extracts user information
 */
export const validateToken = async (token: string): Promise<AuthValidationResult> => {
  try {
    if (!token) {
      return { success: false, error: 'No token provided' };
    }

    // Remove 'Bearer ' prefix if present
    const cleanToken = token.replace('Bearer ', '');
    
    // For now, we'll decode without verification since we don't have the secret
    // In production, you should verify with the actual JWT secret
    const decoded = jwt.decode(cleanToken) as any;
    
    if (!decoded) {
      return { success: false, error: 'Invalid token format' };
    }

    // Extract user information from token
    const user: User = {
      user_id: decoded.user_id || decoded.id,
      user_guid: decoded.user_guid,
      first_name: decoded.first_name,
      last_name: decoded.last_name,
      email: decoded.email,
      role: decoded.role,
      status: decoded.status,
    };

    return { success: true, user };
  } catch (error) {
    console.error('Token validation error:', error);
    return { success: false, error: 'Token validation failed' };
  }
};

/**
 * Middleware to authenticate requests
 */
export const authenticateRequest = async (request: NextRequest): Promise<AuthValidationResult> => {
  const authHeader = request.headers.get('authorization');
  
  if (!authHeader) {
    return { success: false, error: 'Authorization header missing' };
  }

  return validateToken(authHeader);
};

/**
 * Creates standardized API response
 */
export const createApiResponse = (
  statusCode: number,
  message: string,
  data?: any,
  success: boolean = true
) => {
  return {
    statusCode,
    message,
    data,
    success,
  };
};

/**
 * Creates standardized error response
 */
export const createErrorResponse = (
  statusCode: number,
  message: string,
  error?: string
) => {
  return {
    statusCode,
    message,
    error,
    success: false,
  };
};

/**
 * Validates email format
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validates UUID format
 */
export const isValidUUID = (uuid: string): boolean => {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
};

/**
 * Validates OTP format (4-6 characters)
 */
export const isValidOTP = (otp: string): boolean => {
  return /^[0-9]{4,6}$/.test(otp);
};
