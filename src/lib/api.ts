import axios, { AxiosRequestConfig } from 'axios';
const apiLink = process.env.NEXT_PUBLIC_BASE_URL;

export const makeRequest = async (method: string, endpoint: string, data: any, token?: string) => {
  const commonOptions: AxiosRequestConfig = {
    baseURL: `${apiLink ?? ''}/api/v1`,
    method,
    url: endpoint,
    timeout: 50000,
  };

  if (data && Object.keys(data).length > 0) {
    commonOptions.data = data;
  }
  const axiosOptions: AxiosRequestConfig =
    typeof token === 'undefined'
      ? commonOptions
      : {
          ...commonOptions,
          headers: {
            Authorization: `Bearer ${token}`,
          },
        };
  const res = await axios.request({
    ...axiosOptions,
  });
  return res.data;
};

export const makeRequestFormData = async (method: string, endpoint: string, data: any, token?: string) => {
  const commonOptions: AxiosRequestConfig = {
    baseURL: `${apiLink ?? ''}/api/v1`,
    method,
    url: endpoint,
    timeout: 50000,
  };

  if (data) {
    commonOptions.data = data;
  }
  const axiosOptions: AxiosRequestConfig =
    typeof token === 'undefined'
      ? commonOptions
      : {
          ...commonOptions,
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'multipart/form-data',
          },
        };
  console.log(axiosOptions);
  const res = await axios.request({
    ...axiosOptions,
  });
  return res.data;
};
