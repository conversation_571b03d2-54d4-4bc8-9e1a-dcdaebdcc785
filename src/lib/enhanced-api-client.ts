import { apiClient } from './api';
import {
  EnhancedOtpRequest,
  EnhancedOtpResponse,
  EnhancedOtpVerificationRequest,
  EnhancedOtpVerificationResponse,
  RoomMembersResponse,
  CreateRoomWithOrderRequest,
  CreateRoomWithOrderResponse,
  ApiResponse,
} from '@/types/chat';

/**
 * Enhanced API Client for new authentication and room management APIs
 */
export class EnhancedApiClient {
  /**
   * Send OTP with room validation
   */
  static async sendOtpWithValidation(
    request: EnhancedOtpRequest
  ): Promise<EnhancedOtpResponse> {
    const response = await apiClient({
      method: 'POST',
      endpoint: '/api/auth/send-otp-with-validation',
      data: request,
    });
    return response;
  }

  /**
   * Verify OTP with enhanced response
   */
  static async verifyOtpEnhanced(
    request: EnhancedOtpVerificationRequest
  ): Promise<EnhancedOtpVerificationResponse> {
    const response = await apiClient({
      method: 'POST',
      endpoint: '/api/auth/verify-otp',
      data: request,
    });
    return response;
  }

  /**
   * Get room members (requires authentication)
   */
  static async getRoomMembers(
    roomId: string,
    token: string
  ): Promise<RoomMembersResponse> {
    const response = await apiClient({
      method: 'GET',
      endpoint: `/api/room/${roomId}/members`,
      token,
    });
    return response;
  }

  /**
   * Create room with order validation (requires authentication)
   */
  static async createRoomWithOrder(
    request: CreateRoomWithOrderRequest,
    token: string
  ): Promise<CreateRoomWithOrderResponse> {
    const response = await apiClient({
      method: 'POST',
      endpoint: '/api/room/create/with-order',
      data: request,
      token,
    });
    return response;
  }

  /**
   * Validate room access for user
   */
  static async validateRoomAccess(
    roomId: string,
    email: string,
    role: string
  ): Promise<ApiResponse> {
    const response = await apiClient({
      method: 'POST',
      endpoint: `/api/room/${roomId}/validate-access`,
      data: { email, role },
    });
    return response;
  }

  /**
   * Get order information
   */
  static async getOrderInfo(
    orderGuid: string,
    token: string
  ): Promise<ApiResponse> {
    const response = await apiClient({
      method: 'GET',
      endpoint: `/api/order/${orderGuid}`,
      token,
    });
    return response;
  }

  /**
   * Check if room exists for order
   */
  static async getRoomByOrder(
    orderGuid: string,
    token: string
  ): Promise<ApiResponse> {
    const response = await apiClient({
      method: 'GET',
      endpoint: `/api/room/by-order/${orderGuid}`,
      token,
    });
    return response;
  }

  /**
   * Add member to existing room
   */
  static async addRoomMember(
    roomId: string,
    userId: string,
    role: string,
    token: string
  ): Promise<ApiResponse> {
    const response = await apiClient({
      method: 'POST',
      endpoint: `/api/room/${roomId}/add-member`,
      data: { user_id: userId, role },
      token,
    });
    return response;
  }

  /**
   * Validate user membership in room
   */
  static async validateRoomMembership(
    roomId: string,
    token: string
  ): Promise<ApiResponse> {
    const response = await apiClient({
      method: 'GET',
      endpoint: `/api/room/${roomId}/validate-membership`,
      token,
    });
    return response;
  }

  /**
   * Get room information
   */
  static async getRoomInfo(
    roomId: string,
    token: string
  ): Promise<ApiResponse> {
    const response = await apiClient({
      method: 'GET',
      endpoint: `/api/room/${roomId}/info`,
      token,
    });
    return response;
  }
}

/**
 * Convenience functions for common operations
 */

/**
 * Complete enhanced authentication flow
 */
export async function enhancedAuthFlow(
  roomIdentifier: string,
  email: string,
  role: string,
  otp?: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  try {
    if (!otp) {
      // Step 1: Send OTP with room validation
      const otpResponse = await EnhancedApiClient.sendOtpWithValidation({
        room_identifier: roomIdentifier,
        email,
        role: role as any,
      });

      if (otpResponse.statusCode !== 200) {
        return { success: false, error: otpResponse.message };
      }

      return { success: true, data: { step: 'otp_sent', user: otpResponse.data.user } };
    } else {
      // Step 2: Verify OTP
      const verifyResponse = await EnhancedApiClient.verifyOtpEnhanced({
        email,
        otp,
      });

      if (verifyResponse.statusCode !== 200) {
        return { success: false, error: verifyResponse.message };
      }

      return { 
        success: true, 
        data: { 
          step: 'authenticated', 
          user: verifyResponse.data.user,
          token: verifyResponse.data.token 
        } 
      };
    }
  } catch (error) {
    console.error('Enhanced auth flow error:', error);
    return { success: false, error: 'Authentication failed' };
  }
}

/**
 * Create or join room with order validation
 */
export async function createOrJoinRoomWithOrder(
  orderGuid: string,
  role: string,
  token: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  try {
    const response = await EnhancedApiClient.createRoomWithOrder(
      { order_guid: orderGuid, role: role as any },
      token
    );

    if (response.statusCode !== 200) {
      return { success: false, error: response.message };
    }

    return { success: true, data: response.data };
  } catch (error) {
    console.error('Create/join room error:', error);
    return { success: false, error: 'Failed to create or join room' };
  }
}

export default EnhancedApiClient;
