import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export const checkFilesExtNotValidForImage = (file: string) => {
  const ext = file?.toLowerCase().split('.').pop();
  if (ext !== 'jpg' && ext !== 'jpeg' && ext !== 'png' && ext !== 'pdf' && ext !== 'doc' && ext !== 'docx' && ext !== 'heic') {
    return true;
  }
  return false;
};

export const checkFileSizeNotValidForImage = (file: File) => {
  const size = file?.size;
  if (size > 20 * 1024 * 1024) {
    return true;
  }
  return false;
};


export const getFileType = (fileUrl: string) => {
  const extention = fileUrl?.toLowerCase().split('.').pop();
  const ext = extention?.toLowerCase().split('?').shift();
  return ext;
}


export const isImageType = (fileUrl: string) => {
  const ext = getFileType(fileUrl);
  return ext === 'png' || ext === 'jpeg' || ext === 'jpg' || ext === 'heic';
}

export const isPdfType = (fileUrl: string) => {
  const ext = getFileType(fileUrl);
  return ext === 'pdf';
}

export const isDocType = (fileUrl: string) => {
  const ext = getFileType(fileUrl);
  return ext === 'doc' || ext === 'docx';
}