/**
 * Enhanced API Usage Examples
 * 
 * This file demonstrates how to use the four new enhanced APIs
 * with proper error handling and validation.
 */

import { EnhancedApiClient, enhancedAuthFlow, createOrJoinRoomWithOrder } from './enhanced-api-client';

/**
 * Example 1: Complete Enhanced Authentication Flow
 */
export async function exampleEnhancedAuth() {
  const roomIdentifier = 'room_123';
  const email = '<EMAIL>';
  const role = 'USER';

  try {
    // Step 1: Send OTP with room validation
    console.log('Step 1: Sending OTP with room validation...');
    const otpResult = await enhancedAuthFlow(roomIdentifier, email, role);
    
    if (!otpResult.success) {
      console.error('Failed to send OTP:', otpResult.error);
      return;
    }

    console.log('OTP sent successfully:', otpResult.data);

    // Step 2: Verify OTP (in real app, this would be user input)
    const userOtp = '123456'; // This would come from user input
    console.log('Step 2: Verifying OTP...');
    
    const verifyResult = await enhancedAuthFlow(roomIdentifier, email, role, userOtp);
    
    if (!verifyResult.success) {
      console.error('Failed to verify OTP:', verifyResult.error);
      return;
    }

    console.log('Authentication successful:', verifyResult.data);
    return verifyResult.data.token;

  } catch (error) {
    console.error('Enhanced auth example error:', error);
  }
}

/**
 * Example 2: Get Room Members
 */
export async function exampleGetRoomMembers(token: string) {
  const roomId = 'room_123';

  try {
    console.log('Getting room members...');
    const response = await EnhancedApiClient.getRoomMembers(roomId, token);

    if (response.statusCode !== 200) {
      console.error('Failed to get room members:', response.message);
      return;
    }

    console.log('Room members retrieved successfully:');
    console.log('Room:', response.data.room);
    console.log('Total members:', response.data.total_members);
    console.log('Members:', response.data.members);

    return response.data;

  } catch (error) {
    console.error('Get room members example error:', error);
  }
}

/**
 * Example 3: Create Room with Order Validation
 */
export async function exampleCreateRoomWithOrder(token: string) {
  const orderGuid = '550e8400-e29b-41d4-a716-************';
  const role = 'BUSER'; // Provider role

  try {
    console.log('Creating room with order validation...');
    const result = await createOrJoinRoomWithOrder(orderGuid, role, token);

    if (!result.success) {
      console.error('Failed to create room:', result.error);
      return;
    }

    console.log('Room creation successful:');
    console.log('Room:', result.data.room);
    console.log('Room URL:', result.data.room_url);
    console.log('Is new room:', result.data.is_new_room);

    return result.data;

  } catch (error) {
    console.error('Create room with order example error:', error);
  }
}

/**
 * Example 4: Provider Authentication and Room Access
 */
export async function exampleProviderFlow() {
  const roomIdentifier = 'room_456';
  const providerEmail = '<EMAIL>';
  const role = 'BUSER';
  const orderGuid = '550e8400-e29b-41d4-a716-************';

  try {
    // Step 1: Provider authentication
    console.log('Provider authentication flow...');
    const authResult = await enhancedAuthFlow(roomIdentifier, providerEmail, role);
    
    if (!authResult.success) {
      console.error('Provider auth failed:', authResult.error);
      return;
    }

    // Simulate OTP verification
    const verifyResult = await enhancedAuthFlow(roomIdentifier, providerEmail, role, '123456');
    
    if (!verifyResult.success) {
      console.error('Provider OTP verification failed:', verifyResult.error);
      return;
    }

    const token = verifyResult.data.token;
    console.log('Provider authenticated successfully');

    // Step 2: Create/join room with order
    const roomResult = await createOrJoinRoomWithOrder(orderGuid, role, token);
    
    if (!roomResult.success) {
      console.error('Room creation failed:', roomResult.error);
      return;
    }

    console.log('Provider room access successful:', roomResult.data);

    // Step 3: Get room members
    const membersResult = await exampleGetRoomMembers(token);
    
    return {
      token,
      room: roomResult.data,
      members: membersResult,
    };

  } catch (error) {
    console.error('Provider flow example error:', error);
  }
}

/**
 * Example 5: Admin Access Flow
 */
export async function exampleAdminFlow() {
  const roomIdentifier = 'room_789';
  const adminEmail = '<EMAIL>';
  const role = 'SUPPORT_ADMIN';
  const orderGuid = '550e8400-e29b-41d4-a716-************';

  try {
    console.log('Admin access flow...');
    
    // Admin authentication
    const authResult = await enhancedAuthFlow(roomIdentifier, adminEmail, role);
    if (!authResult.success) {
      console.error('Admin auth failed:', authResult.error);
      return;
    }

    const verifyResult = await enhancedAuthFlow(roomIdentifier, adminEmail, role, '123456');
    if (!verifyResult.success) {
      console.error('Admin OTP verification failed:', verifyResult.error);
      return;
    }

    const token = verifyResult.data.token;
    console.log('Admin authenticated successfully');

    // Admin can access any order (bypass validation)
    const roomResult = await createOrJoinRoomWithOrder(orderGuid, role, token);
    if (!roomResult.success) {
      console.error('Admin room access failed:', roomResult.error);
      return;
    }

    console.log('Admin room access successful:', roomResult.data);
    return { token, room: roomResult.data };

  } catch (error) {
    console.error('Admin flow example error:', error);
  }
}

/**
 * Example 6: Error Handling Scenarios
 */
export async function exampleErrorHandling() {
  console.log('Testing error handling scenarios...');

  try {
    // Test invalid email
    const invalidEmailResult = await EnhancedApiClient.sendOtpWithValidation({
      room_identifier: 'room_123',
      email: 'invalid-email',
      role: 'USER',
    });
    console.log('Invalid email test:', invalidEmailResult);

    // Test invalid room
    const invalidRoomResult = await EnhancedApiClient.sendOtpWithValidation({
      room_identifier: 'nonexistent_room',
      email: '<EMAIL>',
      role: 'USER',
    });
    console.log('Invalid room test:', invalidRoomResult);

    // Test invalid OTP
    const invalidOtpResult = await EnhancedApiClient.verifyOtpEnhanced({
      email: '<EMAIL>',
      otp: 'invalid',
    });
    console.log('Invalid OTP test:', invalidOtpResult);

    // Test unauthorized room access
    const unauthorizedResult = await EnhancedApiClient.getRoomMembers('room_123', 'invalid_token');
    console.log('Unauthorized access test:', unauthorizedResult);

  } catch (error) {
    console.error('Error handling example error:', error);
  }
}

/**
 * Run all examples
 */
export async function runAllExamples() {
  console.log('=== Enhanced API Examples ===\n');

  // Example 1: Basic auth flow
  const token = await exampleEnhancedAuth();
  if (!token) return;

  // Example 2: Get room members
  await exampleGetRoomMembers(token);

  // Example 3: Create room with order
  await exampleCreateRoomWithOrder(token);

  // Example 4: Provider flow
  await exampleProviderFlow();

  // Example 5: Admin flow
  await exampleAdminFlow();

  // Example 6: Error handling
  await exampleErrorHandling();

  console.log('\n=== Examples completed ===');
}
