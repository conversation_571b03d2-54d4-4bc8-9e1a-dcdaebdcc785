"use client";
import React, { Suspense, use, useContext, useEffect, useState } from "react";
import "@chatscope/chat-ui-kit-styles/dist/default/styles.min.css";
import {
  ChatContainer,
  ConversationHeader,
  Message,
  MessageList,
  Avatar,
  InfoButton,
  MessageInput,
} from "@chatscope/chat-ui-kit-react";
import { WebSocketContext } from "@/context/websocket";
import { useSearchParams } from "next/navigation";
import {
  checkFilesExtNotValidForImage,
  checkFileSizeNotValidForImage,
  isImageType,
} from "@/lib/utils";
import { toast } from "sonner";
import { makeRequest, makeRequestFormData } from "@/lib/api";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import FileDisplayContainer from "@/components/fileDisplayContainer";
import { CheckCircle } from "lucide-react";
import { Loader } from "../shared/loader";
import { SocketContext } from "@/context/socket";
import useNotification from "@/hooks/useNotification";
export interface IUser {
  user_id: string;
  first_name: string;
  last_name: number;
  messageHistory: any[];
}
type Provider = {
  user_id: string;
};

interface Props {
  token: string;
  patient: IUser;
  provider: IUser;
  room: any;
  role: string;
}

const ChatViewComponent = ({ token, patient, provider, room, role }: Props) => {
  const [messageHistory, setMessageHistory] = useState<any[]>([]);
  const searchParams = useSearchParams();
  const roomIdentifier = searchParams.get("room");
  const [loader, setLoader] = useState(false);
  const providerFullName = provider.first_name + " " + provider.last_name;
  const patientFullName = patient.first_name + " " + patient.last_name;
  const receiverFullName =
    role === "PROVIDER" ? patientFullName : providerFullName;
  const senderFullName =
    role === "PROVIDER" ? providerFullName : patientFullName;
  const senderId = role === "PROVIDER" ? provider.user_id : patient.user_id;
  const roomId = room?.id;

  const [imageModalVisible, setImageModalVisible] = useState(false);
  const [imageModalValue, setImageModalValue] = useState("");
  const { socket, isConnected } = useContext(SocketContext);
  const sendNotification = useNotification();
  const getMessageHistory = async (roomIdentifier: string) => {
    setLoader(true);
    try {
      const res = await makeRequest(
        "GET",
        "chat?roomId=" + roomIdentifier,
        {},
        token ?? "",
      );
      if (res.statusCode === 200) {
        toast.success("Chat room initiated.", {
          duration: 1000,
          closeButton: true,
        });
        setMessageHistory(res.data?.messages ?? []);
      } else {
        toast(res.message, {
          duration: 1000,
          closeButton: true,
        });
      }
    } catch (error) {
      toast("Something went wrong.");
      setLoader(false);
    } finally {
      setLoader(false);
    }
  };

  const readAllMessage = async () => {
    await makeRequest(
      "GET",
      `chat/${roomIdentifier}/read-all-messages`,
      {},
      token ?? "",
    );
  };

  const handledMessageSend = async (message: string) => {
    socket?.emit("send-message", {
      sender_id: senderId,
      message: message,
      type: "room",
      room_id: roomId,
    });
    const newMessage = [
      ...(messageHistory ?? []),
      {
        message: message,
        type: "room",
        roomId: roomId,
        sender: {
          user_id: senderId,
          name: senderFullName,
        },
      },
    ];
    setMessageHistory(newMessage);
  };

  const handleOnAttached = () => {
    const input = document.createElement("input");
    input.type = "file";
    input.accept = "image/*";
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        uploadImage({
          target: { files: [file] },
        } as unknown as React.ChangeEvent<HTMLInputElement>);
      }
    };
    input.click();
  };

  useEffect(() => {
    if (provider && roomId && socket) {
      socket.emit("join", {
        room_id: roomId,
        user_id: senderId,
        name: senderFullName,
      });
    }
  }, [socket, provider, roomId]);

  useEffect(() => {
    socket?.on("receive-message", (data: any) => {
      const newMessage = [...(messageHistory ?? []), data];
      sendNotification({
        title: `New Message from ${receiverFullName}`,
        body: data.message,
      });
      setMessageHistory(newMessage);
      readAllMessage();
    });

    return () => {
      socket?.off("receive-message");
    };
  }, [socket, messageHistory]);

  useEffect(() => {
    getMessageHistory(roomIdentifier ?? "");
    readAllMessage();
  }, []);

  const uploadImage = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const toastId = toast("Sonner");
    if (!e.target.files) return;
    if (checkFilesExtNotValidForImage(e.target.files[0]?.name)) {
      toast("Please upload jpg, jpeg, png, heic, pdf, doc or docx file");

      return;
    }
    if (checkFileSizeNotValidForImage(e.target.files[0])) {
      toast("File size should be below 20 MB");
      return;
    }

    try {
      toast.loading("Uploading...", {
        id: toastId,
      });

      const data = new FormData();
      data.append("file", e.target.files[0]);

      if (senderId && roomId) {
        data.append("user_id", senderId.toString());
        data.append("room_id", roomId?.toString() ?? "");
      }
      //  const token = localStorage.getItem("accessToken");
      const res = await makeRequestFormData("POST", "files/upload", data, "");

      socket?.emit("send-message", {
        message: "ATTACHMENT",
        type: "room",
        file_path: res.fileUrl,
        file_id: res.fileId,
        room_id: roomId,
        sender: {
          user_id: senderId,
          name: senderFullName,
        },
      });

      setMessageHistory((prev) => [
        ...(prev ?? []),
        {
          message: "ATTACHMENT",
          type: "room",
          file_id: res.fileId,
          file: {
            user_file_id: res.fileId,
            path: res.signedUrl,
          },
          sender: {
            user_id: senderId,
            name: senderFullName,
          },
        },
      ]);

      toast.success("Upload Success", {
        id: toastId,
        duration: 1000,
        closeButton: true,
      });
    } catch (error) {
      toast.success("Upload Failed", {
        id: toastId,
        duration: 1000,
        closeButton: true,
      });
    }
  };

  const getFileSignUrl = async (file_id: string) => {
    const toastId = toast("Sonner");

    toast.loading("Loading...", {
      id: toastId,
    });

    const token = localStorage.getItem("accessToken");
    const res = await makeRequest(
      "POST",
      "files/sign-url",
      { file_id },
      token ?? "",
    );
    if (res.statusCode === 200) {
      toast.success(res.message, {
        id: toastId,
      });

      setImageModalVisible(true);
      setImageModalValue(res.data.url);
    } else {
      toast.error(res.message, {
        id: toastId,
      });
    }
  };

  return (
    <Suspense>
      <Loader show={loader} />
      <ChatContainer
        style={{
          height: "100vh",
        }}
      >
        <ConversationHeader>
          <Avatar name={receiverFullName} src={`/images/avatar.png`} />
          <ConversationHeader.Content userName={receiverFullName} />

          <ConversationHeader.Actions>
            {isConnected && <CheckCircle />}
          </ConversationHeader.Actions>
        </ConversationHeader>
        <MessageList>
          {messageHistory &&
            messageHistory?.map((data, idx) => {
              console.log(data);
              const isSenderByMe = data.sender?.user_id === senderId;
              return (
                <Message
                  key={idx}
                  onClick={() => {
                    if (data.file?.path) {
                      getFileSignUrl(data.file?.path);
                    }
                  }}
                  model={{
                    direction: isSenderByMe ? "outgoing" : "incoming",
                    message: data?.message?.replace(/<[^>]*>?/gm, ""),
                    position: "last",
                    sender: data?.user?.name,
                    sentTime: "15 mins ago",
                  }}
                >
                  {/* {data.file?.path && <Message.ImageContent className='cursor-pointer' src={`/images/file.png`} height={200} />} */}
                  {data?.message === "ATTACHMENT" ? (
                    <Message.ImageContent
                      className="cursor-pointer"
                      src={`/images/${
                        isImageType(data.file?.path) ? "file.png" : "pdf.png"
                      }`}
                      height={100}
                    />
                  ) : (
                    <Message.HtmlContent html={data?.message} />
                  )}

                  {isSenderByMe ? (
                    "outgoing"
                  ) : (
                    <Avatar name="" src={`/images/avatar.png`} />
                  )}
                </Message>
              );
            })}
        </MessageList>
        <MessageInput
          autoFocus
          placeholder="Type message here"
          onSend={handledMessageSend}
          onAttachClick={handleOnAttached}
        />
      </ChatContainer>

      <Dialog
        open={imageModalVisible}
        onOpenChange={() => setImageModalVisible(false)}
      >
        <DialogContent className="sm:max-w-[925px] max-h-[700px]">
          <FileDisplayContainer
            src={imageModalValue}
            imageModalVisible={imageModalVisible}
            setImageModalVisible={setImageModalVisible}
          />
        </DialogContent>
      </Dialog>
    </Suspense>
  );
};

export default ChatViewComponent;
