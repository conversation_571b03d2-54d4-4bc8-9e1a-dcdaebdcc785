"use client";
import React, { useEffect, useState } from "react";
import "@chatscope/chat-ui-kit-styles/dist/default/styles.min.css";
import { useSearchParams } from "next/navigation";
import { toast } from "sonner";
import { makeRequest } from "@/lib/api";
import ChatViewComponent from "./chat-view";
export interface IUser {
  user_id: string;
  first_name: string;
  last_name: number;
  messageHistory: any[];
}
type Provider = {
  user_id: string;
};
const ProviderChatUIComponent = ({ token }: { token: string }) => {
  const [provider, setProvider] = useState<IUser>();
  const [patient, setPatient] = useState<IUser>();
  const [roomId, setRoomId] = useState(0);
  const [room, setRoom] = useState<any>();
  const searchParams = useSearchParams();
  const roomIdentifier = searchParams.get("room");

  const getMessageHistory = async (roomIdentifier: string) => {
    try {
      const res = await makeRequest(
        "GET",
        "chat?roomId=" + roomIdentifier,
        {},
        token ?? "",
      );
      if (res.statusCode === 200) {
        toast.success("Chat room initiated.", {
          duration: 1000,
          closeButton: true,
        });
        setRoomId(res.data?.room?.id);
        setRoom(res.data?.room);
        setProvider(res.data?.provider);
        setPatient(res.data?.patient);
      } else {
        toast(res.message);
      }
    } catch (error) {
      toast("Something went wrong.");
    }
  };

  useEffect(() => {
    getMessageHistory(roomIdentifier ?? "");
  }, [roomIdentifier]);

  if (!provider) return null;

  if (!patient) return null;

  if (!roomId) return null;

  return (
    <ChatViewComponent
      token={token}
      patient={patient}
      provider={provider}
      room={room}
      role="PROVIDER"
    />
  );
};

export default ProviderChatUIComponent;
