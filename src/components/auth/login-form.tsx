"use client";
import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Mail, Shield, User, UserCheck } from "lucide-react";
import { useAuth } from "@/context/auth";
import { UserRole } from "@/types/chat";
import { OtpModal } from "@/components/shared/otp-modal";
import { toast } from "sonner";

interface LoginFormProps {
  defaultRole?: UserRole;
  title?: string;
  description?: string;
}

const roleConfig = {
  USER: {
    icon: User,
    label: "Patient",
    description: "Access your consultation chat",
    color: "text-blue-600",
  },
  PROVIDER: {
    icon: User<PERSON>he<PERSON>,
    label: "Healthcare Provider",
    description: "Manage patient consultations",
    color: "text-green-600",
  },
  ADMIN: {
    icon: Shield,
    label: "Administrator",
    description: "System administration access",
    color: "text-purple-600",
  },
};

/**
 * Unified Login Form Component
 * Handles email input and role selection for all user types
 */
export const LoginForm: React.FC<LoginFormProps> = ({
  defaultRole,
  title = "Sign In",
  description = "Enter your email to receive an OTP",
}) => {
  const [email, setEmail] = useState("");
  const [selectedRole, setSelectedRole] = useState<UserRole>(
    defaultRole || "USER",
  );
  const [showOtpModal, setShowOtpModal] = useState(false);
  const { login, verifyOtp, isLoading, error, clearError } = useAuth();

  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email.trim()) {
      toast.error("Please enter your email address");
      return;
    }

    if (!email.includes("@")) {
      toast.error("Please enter a valid email address");
      return;
    }

    clearError();
    const success = await login(email, selectedRole);
    if (success) {
      setShowOtpModal(true);
    }
  };

  const handleOtpVerify = async (otp: string): Promise<boolean> => {
    const success = await verifyOtp(otp);
    if (success) {
      setShowOtpModal(false);
      // Navigation is handled in the auth context
    }
    return success;
  };

  const handleResendOtp = async (): Promise<void> => {
    await login(email, selectedRole);
  };

  const RoleIcon = roleConfig[selectedRole].icon;

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div
            className={`mx-auto h-12 w-12 ${roleConfig[selectedRole].color}`}
          >
            <RoleIcon className="h-12 w-12" />
          </div>
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            {title}
          </h2>
          <p className="mt-2 text-sm text-gray-600">{description}</p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="text-center">
              {roleConfig[selectedRole].label} Login
            </CardTitle>
            <CardDescription className="text-center">
              {roleConfig[selectedRole].description}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleEmailSubmit} className="space-y-6">
              {!defaultRole && (
                <div className="space-y-2">
                  <Label htmlFor="role">Select Role</Label>
                  <Select
                    value={selectedRole}
                    onValueChange={(value: UserRole) => setSelectedRole(value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select your role" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(roleConfig).map(([role, config]) => (
                        <SelectItem key={role} value={role}>
                          <div className="flex items-center space-x-2">
                            <config.icon
                              className={`h-4 w-4 ${config.color}`}
                            />
                            <span>{config.label}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="email">Email Address</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter your email"
                    className="pl-10"
                    required
                    disabled={isLoading}
                  />
                </div>
              </div>

              {error && (
                <div className="text-red-600 text-sm text-center">{error}</div>
              )}

              <Button
                type="submit"
                className="w-full"
                disabled={isLoading || !email.trim()}
              >
                {isLoading ? "Sending OTP..." : "Send OTP"}
              </Button>
            </form>

            <div className="mt-6 text-center">
              <p className="text-xs text-gray-500">
                By signing in, you agree to our terms of service and privacy
                policy.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* OTP Modal */}
        <OtpModal
          isOpen={showOtpModal}
          onClose={() => setShowOtpModal(false)}
          onVerify={handleOtpVerify}
          onResendOtp={handleResendOtp}
          userEmail={email}
          title="Verify Your Identity"
          description={`Please enter the OTP sent to ${email}`}
        />
      </div>
    </div>
  );
};

export default LoginForm;
