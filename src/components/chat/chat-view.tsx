"use client";
import { SocketContext } from "@/context/socket";
import {
  <PERSON><PERSON>,
  ChatContainer,
  ConversationHeader,
  Message,
  MessageInput,
  MessageList,
} from "@chatscope/chat-ui-kit-react";
import { CheckCircle } from "lucide-react";
import React, { use, useContext, useEffect, useState } from "react";
import "@chatscope/chat-ui-kit-styles/dist/default/styles.min.css";
import { Dialog, DialogContent } from "../ui/dialog";
import FileDisplayContainer from "@/components/fileDisplayContainer";
import { makeRequest, makeRequestFormData } from "@/lib/api";
import { Loader } from "../shared/loader";
import { toast } from "sonner";
import {
  checkFilesExtNotValidForImage,
  checkFileSizeNotValidForImage,
  isImageType,
} from "@/lib/utils";
import { patientImage, providerImage } from "@/assets";
import useNotification from "@/hooks/useNotification";
import { Switch } from "../ui/switch";
interface User {
  user_id: string;
  first_name: string;
  last_name: number;
  messageHistory: any[];
}

interface Props {
  patient: User;
  provider: User;
  room: any;
  role: "USER" | "PROVIDER";
  token: string;
  roomIdentifier: string;
}

const ChatViewPage = ({
  patient,
  provider,
  room,
  role,
  token,
  roomIdentifier,
}: Props) => {
  const providerFullName = provider?.first_name + " " + provider?.last_name;
  const patientFullName = patient?.first_name + " " + patient?.last_name;
  const receiverFullName =
    role === "PROVIDER" ? patientFullName : providerFullName;
  const senderFullName =
    role === "PROVIDER" ? providerFullName : patientFullName;
  const roomId = room?.id;
  const { socket, isConnected } = useContext(SocketContext);
  const [messageHistory, setMessageHistory] = useState<any[]>();
  const currentUserId = role === "USER" ? patient?.user_id : provider?.user_id;
  const [imageModalVisible, setImageModalVisible] = useState(false);
  const [imageModalValue, setImageModalValue] = useState("");
  const [loader, setLoader] = useState(false);
  const senderImage = role === "USER" ? patientImage : providerImage;
  const receiverImage = role === "USER" ? providerImage : patientImage;
  const sendNotification = useNotification();

  const getMessageHistory = async () => {
    setLoader(true);
    try {
      const res = await makeRequest(
        "GET",
        `chat/${roomIdentifier}/messages`,
        {},
        token ?? "",
      );
      if (res.statusCode === 200) {
        toast.success("Chat room initiated.", {
          duration: 1000,
          closeButton: true,
        });
        setMessageHistory(res.data?.messages ?? []);
      } else {
        toast(res.message, {
          closeButton: true,
          duration: 1000,
        });
      }
    } catch (error) {
      toast("Something went wrong.");
      setLoader(false);
    } finally {
      setLoader(false);
    }
  };

  const readAllMessage = async () => {
    await makeRequest(
      "GET",
      `chat/${roomIdentifier}/read-all-messages`,
      {},
      token ?? "",
    );
  };

  useEffect(() => {
    getMessageHistory();
    readAllMessage();
  }, [roomId]);

  const handledMessageSend = async (message: string) => {
    socket?.emit("send-message", {
      message: message,
      type: "room",
      room_id: roomId,
      sender: {
        user_id: currentUserId,
        name: senderFullName,
      },
    });
    const newMessage = [
      ...(messageHistory ?? []),
      {
        message: message,
        type: "room",
        roomId: roomId,
        sender: {
          user_id: currentUserId,
          name: senderFullName,
        },
      },
    ];
    setMessageHistory(newMessage);
  };
  const handleOnAttached = () => {
    const input = document.createElement("input");
    input.type = "file";
    input.accept = "image/*";
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        uploadImage({
          target: { files: [file] },
        } as unknown as React.ChangeEvent<HTMLInputElement>);
      }
    };
    input.click();
  };
  const uploadImage = async (e: React.ChangeEvent<HTMLInputElement>) => {
    console.log(e.target.files);
    setLoader(true);
    if (!e.target.files) return;
    if (checkFilesExtNotValidForImage(e.target.files[0]?.name)) {
      toast("Please upload jpg, jpeg, png, heic, pdf, doc or docx file");

      return;
    }

    if (checkFileSizeNotValidForImage(e.target.files[0])) {
      toast("File size should be below 20 MB");
      return;
    }

    try {
      const data = new FormData();
      data.append("file", e.target.files[0]);

      if (currentUserId && roomId) {
        data.append("user_id", currentUserId.toString());
        data.append("room_id", roomId?.toString() ?? "");
      }
      //  const token = localStorage.getItem("accessToken");
      const res = await makeRequestFormData("POST", "files/upload", data, "");

      socket?.emit("send-message", {
        message: "ATTACHMENT",
        type: "room",
        file_path: res.fileUrl,
        file_id: res.fileId,
        room_id: roomId,
        sender: {
          user_id: currentUserId,
          name: senderFullName,
        },
      });

      setMessageHistory((prev) => [
        ...(prev ?? []),
        {
          sender_id: currentUserId,
          message: "ATTACHMENT",
          type: "room",
          file_id: res.fileId,
          file: {
            user_file_id: res.fileId,
            path: res.fileUrl,
          },
          sender: {
            user_id: currentUserId,
            name: senderFullName,
          },
        },
      ]);
    } catch (error) {
      setLoader(false);
    } finally {
      setLoader(false);
    }
  };
  const getFileSignUrl = async (file_id: string) => {
    setLoader(true);

    try {
      const token = localStorage.getItem("accessToken");
      const res = await makeRequest(
        "POST",
        "files/sign-url",
        { file_id },
        token ?? "",
      );
      setImageModalVisible(true);
      setImageModalValue(res.data.url);
    } catch (error) {
      setLoader(false);
    } finally {
      setLoader(false);
    }
  };

  useEffect(() => {
    console.log(Boolean(socket && currentUserId && roomId));
    if (socket && currentUserId && roomId) {
      socket.emit("join", {
        room_id: roomId,
        user_id: currentUserId,
        name: senderFullName,
      });
    }
  }, [socket, currentUserId]);

  // socket?.on('receive-message', (data: any) => {
  //   const newMessage = [...(messageHistory ?? []), data];
  //   sendNotification({
  //     title: `New Message from ${receiverFullName}`,
  //     body: data.message,
  //   })
  //   setMessageHistory(newMessage);
  // });

  const toggleSwitch = async (e: boolean) => {
    try {
      setLoader(true);
      const res = await makeRequest(
        "POST",
        `room/${roomId}/${e ? "enable" : "disable"}`,
        {},
        token ?? "",
      );
    } catch (error) {
      setLoader(false);

      toast.error("Something went wrong while toggling switch.");
      return;
    } finally {
      setLoader(false);
      socket.emit("toggle-room-status", {
        room_id: roomId,
        user_id: currentUserId,
        patient_id: patient?.user_id,
        name: senderFullName,
        room_status: e ? "active" : "inactive",
      });
    }

    // Handle the switch toggle logic here
  };

  useEffect(() => {
    socket?.on("receive-message", (data: any) => {
      const newMessage = [...(messageHistory ?? []), data];
      sendNotification({
        title: `New Message from ${receiverFullName}`,
        body: data.message,
      });
      setMessageHistory(newMessage);
      readAllMessage();
    });
    return () => {
      socket?.off("receive-message");
    };
  }, [socket, messageHistory]);

  return (
    <>
      <Loader show={loader} />
      <Dialog
        open={imageModalVisible}
        onOpenChange={() => setImageModalVisible(false)}
      >
        <DialogContent className="sm:max-w-[925px]">
          <FileDisplayContainer
            src={imageModalValue}
            imageModalVisible={imageModalVisible}
            setImageModalVisible={setImageModalVisible}
          />
        </DialogContent>
      </Dialog>
      <ChatContainer
        style={{
          height: "100vh",
        }}
      >
        <ConversationHeader>
          <Avatar
            name={receiverFullName}
            src={`/images/${
              role === "PROVIDER" ? "patient.png" : "provider.png"
            }`}
          />
          <ConversationHeader.Content userName={receiverFullName} />
          <ConversationHeader.Actions className="flex items-center gap-2">
            <Switch
              id="chat-switch"
              defaultChecked={room.active}
              onCheckedChange={(e) => {
                toggleSwitch(e);
              }}
            />
            {isConnected && <CheckCircle />}
          </ConversationHeader.Actions>
        </ConversationHeader>
        <MessageList>
          {messageHistory &&
            messageHistory?.map((data, idx) => {
              const isSendedByMe = data.sender.user_id === currentUserId;
              return (
                <Message
                  key={idx}
                  onClick={() => {
                    if (data.file?.path) {
                      getFileSignUrl(data.file?.path);
                    }
                  }}
                  model={{
                    direction: isSendedByMe ? "outgoing" : "incoming",
                    // message: data?.message?.replace(/<[^>]*>?/gm, ''),
                    position: "last",
                    sender: senderFullName,
                    sentTime: data?.created_at,
                    type: "html",
                  }}
                >
                  {data?.message === "ATTACHMENT" ? (
                    <Message.ImageContent
                      className="cursor-pointer"
                      src={`/images/${
                        isImageType(data.file?.path) ? "file.png" : "pdf.png"
                      }`}
                      height={100}
                    />
                  ) : (
                    <Message.HtmlContent html={data?.message} />
                  )}

                  {/* {data?.message === "ATTACHMENT" ? <Message.ImageContent className='cursor-pointer' src={`/images/file.png`} height={100} /> : <Message.HtmlContent html={data?.message} />} */}
                  {isSendedByMe ? (
                    <></>
                  ) : (
                    <Avatar
                      name=""
                      src={`/images/${
                        role === "PROVIDER" ? "patient.png" : "provider.png"
                      }`}
                    />
                  )}
                </Message>
              );
            })}
        </MessageList>
        <MessageInput
          autoFocus
          placeholder="Type message here"
          onSend={handledMessageSend}
          onAttachClick={handleOnAttached}
        />
      </ChatContainer>
    </>
  );
};

export default ChatViewPage;
