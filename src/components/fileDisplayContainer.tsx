/* eslint-disable @next/next/no-img-element */
import React, { useEffect, useState } from "react";
import { Dialog, DialogContent } from "./ui/dialog";
import { getFileType, isImageType } from "@/lib/utils";
import { useRouter } from "next/navigation";
import { toast } from "sonner";

interface FileDisplayContainerProps {
  imageModalVisible: boolean;
  setImageModalVisible: React.Dispatch<React.SetStateAction<boolean>>;
  src: string;
}

const FileDisplayContainer = ({
  imageModalVisible,
  setImageModalVisible,
  src,
}: FileDisplayContainerProps) => {
  const ext = getFileType(src);

  const [fileType, setFileType] = useState(getFileType(src));
  const router = useRouter();

  useEffect(() => {
    const type = getFileType(src);
    setFileType(type);
    if (isImageType(src)) {
      setImageModalVisible(true);
    } else {
      setImageModalVisible(false);
      toast.info("File downloaded successfully", {
        closeButton: true,
        duration: 1000,
      });
      router.push(src);
    }
  }, [src]);

  // if (ext === 'png' || ext === 'jpeg' || ext === 'jpg' || ext === 'heic') {
  //   return
  // } else if (ext === 'pdf') {
  //   return <iframe src={src} width={'100%'} height={500} />;
  // } else if (ext === 'doc' || ext === 'docx') {
  //   return <embed src={src} type='application/msword' width={'100%'} height={500} />;
  // }

  if (isImageType(src)) {
    return (
      <Dialog
        open={imageModalVisible}
        onOpenChange={() => setImageModalVisible(false)}
      >
        <DialogContent className="sm:max-w-[925px]">
          <img src={src} alt="preview" className="max-h-[700px]" />;
        </DialogContent>
      </Dialog>
    );
  }

  return <></>;
};

export default FileDisplayContainer;
