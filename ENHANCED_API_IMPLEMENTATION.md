# Enhanced API Implementation

This document describes the implementation of the four new enhanced APIs for the provider portal chat module.

## 🚀 Implemented APIs

### 1. Enhanced OTP Sending with Room Validation
- **Endpoint**: `POST /api/auth/send-otp-with-validation`
- **File**: `src/app/api/auth/send-otp-with-validation/route.ts`
- **Features**:
  - Room validation before sending OTP
  - User-room membership verification
  - Enhanced role validation
  - Comprehensive error handling

### 2. Enhanced OTP Verification
- **Endpoint**: `POST /api/auth/verify-otp`
- **File**: `src/app/api/auth/verify-otp/route.ts`
- **Features**:
  - Email-based OTP verification
  - Enhanced user profile retrieval
  - JWT token generation with 2-hour expiration
  - Improved validation and error handling

### 3. Get Room Members (Token Protected)
- **Endpoint**: `GET /api/room/[roomId]/members`
- **File**: `src/app/api/room/[roomId]/members/route.ts`
- **Features**:
  - JWT token authentication
  - Room membership validation
  - Complete member information retrieval
  - Role-based access control

### 4. Advanced Room Creation with Order Validation
- **Endpoint**: `POST /api/room/create/with-order`
- **File**: `src/app/api/room/create/with-order/route.ts`
- **Features**:
  - Order GUID validation
  - Provider-user relationship verification
  - Existing room detection and member addition
  - Role-based authorization logic

## 📁 File Structure

```
src/
├── app/api/
│   ├── auth/
│   │   ├── send-otp-with-validation/route.ts
│   │   └── verify-otp/route.ts
│   └── room/
│       ├── [roomId]/members/route.ts
│       └── create/with-order/route.ts
├── lib/
│   ├── auth-middleware.ts          # Authentication utilities
│   ├── enhanced-api-client.ts      # Client-side API wrapper
│   └── enhanced-api-examples.ts    # Usage examples
└── types/
    └── chat.ts                     # Enhanced type definitions
```

## 🔧 Key Components

### Authentication Middleware (`src/lib/auth-middleware.ts`)
- JWT token validation
- User authentication
- Input validation utilities
- Standardized response formatting

### Enhanced API Client (`src/lib/enhanced-api-client.ts`)
- Type-safe API client wrapper
- Convenience functions for common operations
- Error handling and response formatting

### Type Definitions (`src/types/chat.ts`)
- Enhanced user roles
- Request/response interfaces
- API endpoint constants

## 🎯 Usage Examples

### Basic Authentication Flow
```typescript
import { enhancedAuthFlow } from '@/lib/enhanced-api-client';

// Send OTP with room validation
const otpResult = await enhancedAuthFlow('room_123', '<EMAIL>', 'USER');

// Verify OTP
const authResult = await enhancedAuthFlow('room_123', '<EMAIL>', 'USER', '123456');
```

### Get Room Members
```typescript
import { EnhancedApiClient } from '@/lib/enhanced-api-client';

const members = await EnhancedApiClient.getRoomMembers('room_123', token);
```

### Create Room with Order
```typescript
import { createOrJoinRoomWithOrder } from '@/lib/enhanced-api-client';

const room = await createOrJoinRoomWithOrder(orderGuid, 'BUSER', token);
```

## 🔐 Security Features

### Enhanced Validation
- Email format validation
- UUID format validation
- OTP format validation (4-6 digits)
- Role-based access control

### Authentication & Authorization
- JWT token-based authentication
- Role-specific permissions
- Order-user relationship validation
- Room membership verification

### Error Handling
- Consistent error response format
- Detailed error messages for debugging
- Proper HTTP status codes
- Security-conscious error messages

## 🎭 Role-Based Access Control

### User Roles
- **USER**: Patient access, must be order owner
- **BUSER**: Provider access, must be assigned provider
- **SUPPORT_ADMIN**: Admin access, can access any order
- **GROUP_ADMIN**: Admin access, can access any order
- **medical_assistant**: Organization-based access
- **pharmacist**: Organization-based access

### Access Validation Logic
```typescript
// Provider must be assigned to the order
if (role === 'BUSER') {
  return order.provider_id === user.user_id;
}

// User must be the patient
if (role === 'USER') {
  return order.answer_given_by === user.user_id;
}

// Admins can access any order
if (role === 'SUPPORT_ADMIN' || role === 'GROUP_ADMIN') {
  return true;
}
```

## 🧪 Testing

Run the examples to test the implementation:

```typescript
import { runAllExamples } from '@/lib/enhanced-api-examples';

// Run comprehensive test suite
await runAllExamples();
```

## 🔄 Integration with Existing System

### Frontend Integration
The enhanced APIs integrate seamlessly with the existing authentication context:

```typescript
// Update existing auth context to use enhanced APIs
const { user, token } = useAuth();

// Use enhanced room creation
const room = await createOrJoinRoomWithOrder(orderGuid, user.role, token);
```

### Backend Dependencies
The APIs expect the following backend endpoints to exist:
- `GET /room/{roomId}/validate`
- `POST /auth/validate-user-room-access`
- `POST /auth/send-otp`
- `GET /order/{orderGuid}`
- `GET /room/by-order/{orderGuid}`
- `POST /room/create`

## 📋 Required Backend APIs

To complete the implementation, the following backend APIs need to be implemented:

1. **Room Validation**: `GET /room/{roomId}/validate`
2. **User-Room Access Validation**: `POST /auth/validate-user-room-access`
3. **Order Information**: `GET /order/{orderGuid}`
4. **Room by Order**: `GET /room/by-order/{orderGuid}`
5. **Room Creation**: `POST /room/create`
6. **Add Room Member**: `POST /room/{roomId}/add-member`
7. **Validate Membership**: `GET /room/{roomId}/validate-membership`
8. **Room Info**: `GET /room/{roomId}/info`

## 🚀 Next Steps

1. **Backend Implementation**: Implement the required backend APIs
2. **Database Schema**: Ensure proper database relationships exist
3. **JWT Configuration**: Configure JWT secret and validation
4. **Testing**: Comprehensive testing with real data
5. **Documentation**: API documentation for backend team
6. **Integration**: Update frontend components to use enhanced APIs

## 📞 Support

For questions or issues with the enhanced API implementation, please refer to:
- Type definitions in `src/types/chat.ts`
- Usage examples in `src/lib/enhanced-api-examples.ts`
- Authentication middleware in `src/lib/auth-middleware.ts`
